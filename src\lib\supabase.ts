import { createClient, type SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/types'

const supabaseUrl: string = import.meta.env.VITE_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey: string = import.meta.env.VITE_SUPABASE_ANON_KEY;
if (!supabaseAnonKey) {
  throw new Error('Missing Supabase anonymous key. Please set VITE_SUPABASE_ANON_KEY in your environment variables.');
}

// Check if we have real Supabase credentials
export const hasSupabaseConfig: boolean = !!(
  import.meta.env.VITE_SUPABASE_URL &&
  import.meta.env.VITE_SUPABASE_ANON_KEY &&
  import.meta.env.VITE_SUPABASE_URL !== 'your_supabase_project_url' &&
  import.meta.env.VITE_SUPABASE_ANON_KEY !== 'your_supabase_anon_key' &&
  import.meta.env.VITE_SUPABASE_URL.startsWith('https://') &&
  import.meta.env.VITE_SUPABASE_URL.includes('.supabase.co')
)

// Create a singleton Supabase client to avoid multiple instances during HMR
// Store on globalThis to persist across hot reloads
const SUPABASE_KEY = Symbol.for('supabase.client')

// Extend globalThis to include our Supabase client
declare global {
  var [SUPABASE_KEY]: SupabaseClient<Database> | undefined
}

function getSupabaseClient(): SupabaseClient<Database> {
  if (!globalThis[SUPABASE_KEY]) {
    globalThis[SUPABASE_KEY] = createClient<Database>(supabaseUrl, supabaseAnonKey)
  }
  return globalThis[SUPABASE_KEY]
}

export const supabase: SupabaseClient<Database> = getSupabaseClient()
