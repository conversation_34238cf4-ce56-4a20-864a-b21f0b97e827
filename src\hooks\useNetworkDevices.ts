import { useState, useEffect } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'
import type { NetworkDevice, UseNetworkDevicesReturn } from '@/types'

/**
 * Custom hook for network device management operations
 */
export const useNetworkDevices = (): UseNetworkDevicesReturn => {
  const [devices, setDevices] = useState<NetworkDevice[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all network devices with building information
  useEffect(() => {
    const fetchDevices = async () => {
      if (!hasSupabaseConfig) {
        setError('Supabase not configured. Please add your Supabase URL and API key to .env.local')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('network_devices')
          .select(`
            *,
            building:buildings(
              id,
              name,
              address,
              building_code
            )
          `)
          .order('station_name')

        if (error) throw error

        setDevices(data || [])
        
      } catch (err: unknown) {
        console.error('Error fetching network devices:', err)
        setError(err instanceof Error ? err.message : 'Unknown error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchDevices()
  }, [])

  /**
   * Create a new network device
   */
  const createDevice = async (deviceData: Omit<NetworkDevice, 'id' | 'created_at' | 'updated_at'>, debugMode: boolean = false): Promise<{ success: boolean; data?: NetworkDevice; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Validate required fields before processing (only station_name and building_id are required)
      const requiredFields = ['building_id', 'station_name']
      const missingFields = requiredFields.filter(field => !deviceData[field] || String(deviceData[field]).trim() === '')

      if (missingFields.length > 0) {
        const error = `Missing required fields: ${missingFields.join(', ')}`
        if (debugMode) {
          console.error('Validation Error:', error)
          console.error('Device Data:', deviceData)
        }
        return { success: false, error, type: 'validation' }
      }

      // Encrypt passwords if provided
      const processedData = { ...deviceData }

      // Handle password fields - encrypt and convert to encrypted fields
      // Always delete the non-encrypted field names to prevent schema errors
      if (processedData.station_password && typeof processedData.station_password === 'string' && processedData.station_password.trim() !== '') {
        // Import encryption function dynamically to avoid circular dependencies
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.station_password_encrypted = encryptPassword(processedData.station_password)
      }
      delete processedData.station_password

      if (processedData.windows_password && typeof processedData.windows_password === 'string' && processedData.windows_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.windows_password_encrypted = encryptPassword(processedData.windows_password)
      }
      delete processedData.windows_password

      if (processedData.platform_password && typeof processedData.platform_password === 'string' && processedData.platform_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.platform_password_encrypted = encryptPassword(processedData.platform_password)
      }
      delete processedData.platform_password

      if (processedData.passphrase && typeof processedData.passphrase === 'string' && processedData.passphrase.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.passphrase_encrypted = encryptPassword(processedData.passphrase)
      }
      delete processedData.passphrase

      // Clean up empty string values for optional fields
      Object.keys(processedData).forEach(key => {
        if (processedData[key] === '') {
          processedData[key] = null
        }
      })

      if (debugMode) {
        console.log('Processed Device Data:', processedData)
      }

      const { data, error } = await supabase
        .from('network_devices')
        .insert(processedData)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) {
        if (debugMode) {
          console.error('Supabase Error Details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          })
          console.error('Request Payload:', processedData)
        }

        // Categorize error types for better user feedback
        let errorType = 'database'
        let userFriendlyMessage = error.message

        if (error.code === '23505') { // Unique constraint violation
          errorType = 'constraint'
          if (error.message.includes('unique_building_host_id')) {
            userFriendlyMessage = `Host ID "${processedData.host_id}" already exists for this building`
          } else if (error.message.includes('unique_building_ip')) {
            userFriendlyMessage = `IP address "${processedData.ip_address}" already exists for this building`
          } else if (error.message.includes('host_id_key')) {
            userFriendlyMessage = `Host ID "${processedData.host_id}" already exists in the system`
          } else {
            userFriendlyMessage = 'A device with these details already exists'
          }
        } else if (error.code === '23502') { // Not null constraint violation
          errorType = 'validation'
          userFriendlyMessage = `Required field is missing: ${error.message}`
        } else if (error.code === '23503') { // Foreign key constraint violation
          errorType = 'reference'
          userFriendlyMessage = 'Invalid building reference'
        }

        throw new Error(userFriendlyMessage)
      }

      // Update local state
      setDevices(prev => [...prev, data].sort((a, b) => a.station_name.localeCompare(b.station_name)))

      return { success: true, data }

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('Error creating network device:', {
        message: errorMessage,
        deviceData: debugMode ? deviceData : 'Enable debug mode for details'
      })
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Update an existing network device
   */
  const updateDevice = async (id: string, updates: Partial<NetworkDevice>): Promise<{ success: boolean; data?: NetworkDevice; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Process passwords
      const processedData = { ...updates }

      // Handle password fields - encrypt and convert to encrypted fields
      // Always delete the non-encrypted field names to prevent schema errors
      if (processedData.station_password && typeof processedData.station_password === 'string' && processedData.station_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.station_password_encrypted = encryptPassword(processedData.station_password)
      }
      delete processedData.station_password

      if (processedData.windows_password && typeof processedData.windows_password === 'string' && processedData.windows_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.windows_password_encrypted = encryptPassword(processedData.windows_password)
      }
      delete processedData.windows_password

      if (processedData.platform_password && typeof processedData.platform_password === 'string' && processedData.platform_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.platform_password_encrypted = encryptPassword(processedData.platform_password)
      }
      delete processedData.platform_password

      if (processedData.passphrase && typeof processedData.passphrase === 'string' && processedData.passphrase.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.passphrase_encrypted = encryptPassword(processedData.passphrase)
      }
      delete processedData.passphrase

      const { data, error } = await supabase
        .from('network_devices')
        .update(processedData)
        .eq('id', id)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) throw error

      // Update local state
      setDevices(prev =>
        prev.map(device =>
          device.id === id ? data : device
        ).sort((a, b) => a.station_name.localeCompare(b.station_name))
      )

      return { success: true, data }
      
    } catch (err: unknown) {
      console.error('Error updating network device:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Delete a network device
   */
  const deleteDevice = async (id: string): Promise<{ success: boolean; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { error } = await supabase
        .from('network_devices')
        .delete()
        .eq('id', id)

      if (error) throw error

      // Update local state
      setDevices(prev => prev.filter(device => device.id !== id))

      return { success: true }
      
    } catch (err: unknown) {
      console.error('Error deleting network device:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Toggle device active status
   */
  const toggleDeviceStatus = async (id: string): Promise<{ success: boolean; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Get current device to toggle status
      const currentDevice = devices.find(d => d.id === id)
      if (!currentDevice) {
        return { success: false, error: 'Device not found' }
      }

      const { data, error } = await supabase
        .from('network_devices')
        .update({ status: currentDevice.status === 'active' ? 'inactive' : 'active' })
        .eq('id', id)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) throw error

      // Update local state
      setDevices(prev =>
        prev.map(device =>
          device.id === id ? data : device
        )
      )

      return { success: true, data }
      
    } catch (err: unknown) {
      console.error('Error toggling device status:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Check if host ID is unique within a building
   */
  const isHostIdUnique = async (hostId: string, excludeId?: string): Promise<boolean> => {
    if (!hasSupabaseConfig) {
      return false
    }

    try {
      let query = supabase
        .from('network_devices')
        .select('id')
        .eq('host_id', hostId)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error checking host ID uniqueness:', error)
        return false
      }

      return !data || data.length === 0
    } catch (err: unknown) {
      console.error('Error checking host ID uniqueness:', err)
      return false
    }
  }

  /**
   * Check if IP address is unique within a building
   */
  const isIpAddressUnique = async (ipAddress: string, excludeId?: string): Promise<boolean> => {
    if (!hasSupabaseConfig) {
      return false
    }

    try {
      let query = supabase
        .from('network_devices')
        .select('id')
        .eq('ip_address', ipAddress)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error checking IP address uniqueness:', error)
        return false
      }

      return !data || data.length === 0
    } catch (err: unknown) {
      console.error('Error checking IP address uniqueness:', err)
      return false
    }
  }

  /**
   * Get devices filtered by building
   */
  const getDevicesByBuilding = (buildingId: string): NetworkDevice[] => {
    return devices.filter(device => device.building_id === buildingId)
  }

  /**
   * Get devices filtered by type
   */
  const getDevicesByType = (deviceType: string): NetworkDevice[] => {
    return devices.filter(device => device.device_type === deviceType)
  }

  /**
   * Get devices filtered by status
   */
  const getDevicesByStatus = (status: string): NetworkDevice[] => {
    return devices.filter(device => device.status === status)
  }

  /**
   * Search devices by name, host ID, or IP address
   */
  const searchDevices = (searchTerm: string): NetworkDevice[] => {
    if (!searchTerm) return devices

    const term = searchTerm.toLowerCase()
    return devices.filter(device =>
      (device.station_name?.toLowerCase() || '').includes(term) ||
      (device.host_id?.toLowerCase() || '').includes(term) ||
      (device.ip_address || '').includes(term) ||
      (device.device_type?.toLowerCase() || '').includes(term) ||
      (device.building?.name?.toLowerCase() || '').includes(term)
    )
  }

  /**
   * Validate a batch of devices for uniqueness conflicts
   */
  const validateDeviceBatch = async (deviceList: Partial<NetworkDevice>[]): Promise<{ valid: boolean; errors: string[] }> => {
    const errors: string[] = []
    const hostIdMap = new Map<string, number>()
    const ipAddressMap = new Map<string, number>()

    // Check for duplicates within the batch
    deviceList.forEach((device, index) => {
      const rowNumber = index + 1

      // Check host ID duplicates within batch (only if host_id is provided)
      if (device.host_id && device.host_id.trim() !== '') {
        const key = `${device.building_id}-${device.host_id}`
        if (hostIdMap.has(key)) {
          errors.push(`Row ${rowNumber}: Host ID "${device.host_id}" is duplicated in the import (also in row ${hostIdMap.get(key)})`)
        } else {
          hostIdMap.set(key, rowNumber)
        }
      }

      // Check IP address duplicates within batch (only if ip_address is provided)
      if (device.ip_address && device.ip_address.trim() !== '') {
        const key = `${device.building_id}-${device.ip_address}`
        if (ipAddressMap.has(key)) {
          errors.push(`Row ${rowNumber}: IP address "${device.ip_address}" is duplicated in the import (also in row ${ipAddressMap.get(key)})`)
        } else {
          ipAddressMap.set(key, rowNumber)
        }
      }
    })

    // Check for conflicts with existing devices in database (only for provided values)
    for (let i = 0; i < deviceList.length; i++) {
      const device = deviceList[i]
      const rowNumber = i + 1

      if (device.host_id && device.host_id.trim() !== '') {
        const isUnique = await isHostIdUnique(device.host_id)
        if (!isUnique) {
          errors.push(`Row ${rowNumber}: Host ID "${device.host_id}" already exists`)
        }
      }

      if (device.ip_address && device.ip_address.trim() !== '') {
        const isUnique = await isIpAddressUnique(device.ip_address)
        if (!isUnique) {
          errors.push(`Row ${rowNumber}: IP address "${device.ip_address}" already exists`)
        }
      }
    }

    return { valid: errors.length === 0, errors }
  }

  return {
    // Data
    devices,
    loading,
    error,

    // Actions
    createDevice,
    updateDevice,
    deleteDevice,
    toggleDeviceStatus,
    isHostIdUnique,
    isIpAddressUnique,
    validateDeviceBatch,

    // Utilities
    getDevicesByBuilding,
    getDevicesByType,
    getDevicesByStatus,
    searchDevices
  }
}
