/**
 * Custom hook for managing Network Device Management state
 * Centralizes all state logic and reduces component complexity
 */
import { useState, useEffect } from 'react'
import { useNetworkDevices } from './useNetworkDevices'
import { useBuildings } from './useBuildings'
import { useAuth } from '../contexts/AuthContext'
import {
  autoSaveNetworkDeviceFormData,
  loadAutoSavedNetworkDeviceFormData,
  clearAutoSavedNetworkDeviceFormData
} from '../lib/networkDeviceValidation'
import type { NetworkDevice, Building } from '@/types'

interface UseNetworkDeviceStateReturn {
  // Auth & Data
  user: any
  devices: NetworkDevice[]
  buildings: Building[]
  loading: boolean
  error: string | null

  // UI State
  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedBuilding: string
  setSelectedBuilding: (building: string) => void
  selectedDeviceType: string
  setSelectedDeviceType: (type: string) => void
  showForm: boolean
  setShowForm: (show: boolean) => void
  editingDevice: NetworkDevice | null
  setEditingDevice: (device: NetworkDevice | null) => void
  notification: any
  setNotification: (notification: any) => void
  showDeleteConfirm: string | null
  setShowDeleteConfirm: (id: string | null) => void

  // Form State
  formData: Partial<NetworkDevice>
  setFormData: (data: Partial<NetworkDevice>) => void
  formErrors: Record<string, string>
  setFormErrors: (errors: Record<string, string>) => void
  formWarnings: Record<string, string>
  setFormWarnings: (warnings: Record<string, string>) => void
  isSubmitting: boolean
  setIsSubmitting: (submitting: boolean) => void
  showPasswords: Record<string, boolean>
  setShowPasswords: (passwords: Record<string, boolean>) => void

  // Import State
  showImportModal: boolean
  setShowImportModal: (show: boolean) => void
  importFile: File | null
  setImportFile: (file: File | null) => void
  importPreview: any[]
  setImportPreview: (preview: any[]) => void
  importErrors: string[]
  setImportErrors: (errors: string[]) => void
  isImporting: boolean
  setIsImporting: (importing: boolean) => void

  // Pagination State
  currentPage: number
  setCurrentPage: (page: number) => void
  itemsPerPage: number
  setItemsPerPage: (items: number) => void

  // All hook methods
  [key: string]: any
}

export const useNetworkDeviceState = (): UseNetworkDeviceStateReturn => {
  const { user } = useAuth()
  const networkDevicesHook = useNetworkDevices()
  const buildingsHook = useBuildings()

  // UI State
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedBuilding, setSelectedBuilding] = useState('')
  const [selectedDeviceType, setSelectedDeviceType] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingDevice, setEditingDevice] = useState(null)
  const [notification, setNotification] = useState(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null)

  // Form State
  const [formData, setFormData] = useState({
    building_id: '',
    station_name: '',
    device_type: '',
    host_id: '',
    ip_address: '',
    subnet_mask: '*************',
    gateway: '',
    internal_dns_server_1: '',
    internal_dns_server_2: '',
    station_username: '',
    station_password: '',
    windows_username: '',
    windows_password: '',
    platform_username: '',
    platform_password: '',
    passphrase: '',
    software_version: '',
    notes: '',
    is_active: true
  })
  const [formErrors, setFormErrors] = useState({})
  const [formWarnings, setFormWarnings] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPasswords, setShowPasswords] = useState({
    station: false,
    windows: false,
    platform: false,
    passphrase: false
  })

  // Import State
  const [showImportModal, setShowImportModal] = useState(false)
  const [importFile, setImportFile] = useState(null)
  const [importPreview, setImportPreview] = useState(null)
  const [importErrors, setImportErrors] = useState([])
  const [isImporting, setIsImporting] = useState(false)

  // Pagination State
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(50)

  // Filtered devices computation
  const filteredDevices = networkDevicesHook.devices.filter(device => {
    const matchesSearch = !searchTerm ||
      (device.station_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (device.host_id?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (device.ip_address || '').includes(searchTerm) ||
      (device.device_type?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (device.building?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase())

    const matchesBuilding = !selectedBuilding || device.building_id === selectedBuilding
    const matchesType = !selectedDeviceType || device.device_type === selectedDeviceType

    return matchesSearch && matchesBuilding && matchesType
  })

  // Pagination calculations
  const totalPages = Math.ceil(filteredDevices.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentPageDevices = filteredDevices.slice(startIndex, endIndex)

  // Auto-save form data effect
  useEffect(() => {
    if (showForm && (formData.station_name || formData.ip_address)) {
      const formId = editingDevice ? editingDevice.id : 'new'
      autoSaveNetworkDeviceFormData(formId, formData)
    }
  }, [formData, showForm, editingDevice])

  // Load auto-saved data effect
  useEffect(() => {
    if (showForm && !editingDevice) {
      const savedData = loadAutoSavedNetworkDeviceFormData('new')
      if (savedData) {
        setFormData(savedData)
        showNotification('Draft data restored', 'info')
      }
    }
  }, [showForm, editingDevice])

  // Notification helper
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 5000)
  }

  // Reset form helper
  const resetForm = () => {
    setFormData({
      building_id: '',
      station_name: '',
      device_type: '',
      host_id: '',
      ip_address: '',
      subnet_mask: '*************',
      gateway: '',
      internal_dns_server_1: '',
      internal_dns_server_2: '',
      station_username: '',
      station_password: '',
      windows_username: '',
      windows_password: '',
      platform_username: '',
      platform_password: '',
      passphrase: '',
      software_version: '',
      notes: '',
      is_active: true
    })
    setFormErrors({})
    setFormWarnings({})
    setEditingDevice(null)
    setShowPasswords({ station: false, windows: false, platform: false, passphrase: false })
    clearAutoSavedNetworkDeviceFormData(editingDevice ? editingDevice.id : 'new')
  }

  return {
    // Auth & Data
    user,
    ...networkDevicesHook,
    ...buildingsHook,

    // UI State
    searchTerm,
    setSearchTerm,
    selectedBuilding,
    setSelectedBuilding,
    selectedDeviceType,
    setSelectedDeviceType,
    showForm,
    setShowForm,
    editingDevice,
    setEditingDevice,
    notification,
    setNotification,
    showDeleteConfirm,
    setShowDeleteConfirm,

    // Form State
    formData,
    setFormData,
    formErrors,
    setFormErrors,
    formWarnings,
    setFormWarnings,
    isSubmitting,
    setIsSubmitting,
    showPasswords,
    setShowPasswords,

    // Import State
    showImportModal,
    setShowImportModal,
    importFile,
    setImportFile,
    importPreview,
    setImportPreview,
    importErrors,
    setImportErrors,
    isImporting,
    setIsImporting,

    // Pagination State
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,

    // Computed Values
    filteredDevices,
    totalPages,
    startIndex,
    endIndex,
    currentPageDevices,

    // Helper Functions
    showNotification,
    resetForm
  }
}
