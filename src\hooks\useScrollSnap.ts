import { useEffect, useRef, useCallback } from 'react'

interface UseScrollSnapOptions {
  visibleRows?: number
  rowHeight?: number | 'auto'
  enabled?: boolean
}

interface UseScrollSnapReturn {
  containerRef: React.RefObject<HTMLDivElement>
  scrollToRow: (rowIndex: number) => void
  scrollToTop: () => void
  scrollToBottom: () => void
}

/**
 * Custom hook for managing scroll-snap behavior in tables
 */
export const useScrollSnap = ({
  visibleRows = 5,
  rowHeight = 'auto',
  enabled = true
}: UseScrollSnapOptions = {}): UseScrollSnapReturn => {
  const containerRef = useRef<HTMLDivElement>(null)
  const isScrollingRef = useRef<boolean>(false)

  const calculateRowHeight = useCallback((): number => {
    const container = containerRef.current
    if (!container) return 48

    const firstRow = container.querySelector('tbody tr')
    if (firstRow) {
      const rect = firstRow.getBoundingClientRect()
      return Math.max(rect.height, 40) // Minimum height of 40px
    }
    return 48 // fallback height
  }, [])

  const setupScrollSnap = useCallback((): (() => void) | undefined => {
    const container = containerRef.current
    if (!container || !enabled) return

    const calculatedRowHeight = rowHeight === 'auto' ? calculateRowHeight() : rowHeight
    const containerHeight = calculatedRowHeight * visibleRows

    // Set container styles
    container.style.height = `${containerHeight}px`
    container.style.overflowY = 'auto'
    container.style.scrollSnapType = 'y mandatory'
    container.style.scrollBehavior = 'smooth'

    // Apply scroll snap to rows
    const rows = container.querySelectorAll('tbody tr')
    rows.forEach((row, index) => {
      row.style.scrollSnapAlign = 'start'
      row.style.scrollSnapStop = 'always'
      
      // Add data attribute for easier targeting
      row.setAttribute('data-row-index', index)
    })

    // Add keyboard navigation support
    const handleKeyDown = (event) => {
      if (!container.contains(event.target)) return

      const currentScrollTop = container.scrollTop
      const rowHeight = calculatedRowHeight

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          container.scrollTo({
            top: currentScrollTop + rowHeight,
            behavior: 'smooth'
          })
          break
        case 'ArrowUp':
          event.preventDefault()
          container.scrollTo({
            top: Math.max(0, currentScrollTop - rowHeight),
            behavior: 'smooth'
          })
          break
        case 'PageDown':
          event.preventDefault()
          container.scrollTo({
            top: currentScrollTop + (rowHeight * visibleRows),
            behavior: 'smooth'
          })
          break
        case 'PageUp':
          event.preventDefault()
          container.scrollTo({
            top: Math.max(0, currentScrollTop - (rowHeight * visibleRows)),
            behavior: 'smooth'
          })
          break
        case 'Home':
          if (event.ctrlKey) {
            event.preventDefault()
            container.scrollTo({
              top: 0,
              behavior: 'smooth'
            })
          }
          break
        case 'End':
          if (event.ctrlKey) {
            event.preventDefault()
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            })
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [visibleRows, rowHeight, enabled, calculateRowHeight])

  const scrollToRow = useCallback((rowIndex: number): void => {
    const container = containerRef.current
    if (!container) return

    const calculatedRowHeight = rowHeight === 'auto' ? calculateRowHeight() : rowHeight
    const targetScrollTop = rowIndex * calculatedRowHeight

    container.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })
  }, [rowHeight, calculateRowHeight])

  const getCurrentVisibleRows = useCallback((): { start: number; end: number } => {
    const container = containerRef.current
    if (!container) return { start: 0, end: 0 }

    const calculatedRowHeight = rowHeight === 'auto' ? calculateRowHeight() : rowHeight
    const scrollTop = container.scrollTop
    const startRow = Math.floor(scrollTop / calculatedRowHeight)
    const endRow = Math.min(startRow + visibleRows, container.querySelectorAll('tbody tr').length)

    return { start: startRow, end: endRow }
  }, [visibleRows, rowHeight, calculateRowHeight])

  const scrollToTop = useCallback((): void => {
    const container = containerRef.current
    if (!container) return

    container.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, [])

  const scrollToBottom = useCallback((): void => {
    const container = containerRef.current
    if (!container) return

    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth'
    })
  }, [])

  useEffect(() => {
    if (!enabled) return

    const cleanup = setupScrollSnap()

    // Handle window resize
    const handleResize = () => {
      setTimeout(setupScrollSnap, 100)
    }

    window.addEventListener('resize', handleResize)

    // Observe DOM changes
    const container = containerRef.current
    if (container) {
      const observer = new MutationObserver(() => {
        setTimeout(setupScrollSnap, 50)
      })

      observer.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      })

      return () => {
        window.removeEventListener('resize', handleResize)
        observer.disconnect()
        if (cleanup) cleanup()
      }
    }

    return () => {
      window.removeEventListener('resize', handleResize)
      if (cleanup) cleanup()
    }
  }, [setupScrollSnap, enabled])

  return {
    containerRef,
    scrollToRow,
    scrollToTop,
    scrollToBottom
  }
}

export default useScrollSnap
