import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(() => Promise.resolve({ data: { session: null } })),
      onAuthStateChange: vi.fn((callback: (event: string, session: any) => void) => {
        // Call the callback immediately for testing
        setTimeout(() => callback('INITIAL_SESSION', null), 0)
        return { data: { subscription: { unsubscribe: vi.fn() } } }
      }),
      signInWithPassword: vi.fn(() => Promise.resolve({ data: { user: null }, error: null })),
      signUp: vi.fn(() => Promise.resolve({ data: { user: null }, error: null })),
      signOut: vi.fn(() => Promise.resolve({ error: null })),
    },
    from: vi.fn((table: string) => {
      const mockChain = {
        select: vi.fn(() => mockChain),
        order: vi.fn(() => mockChain),
        eq: vi.fn(() => mockChain),
        single: vi.fn(() => Promise.resolve({ data: null, error: null })),
        insert: vi.fn(() => mockChain),
        update: vi.fn(() => mockChain),
        delete: vi.fn(() => mockChain),
        // Add Promise resolution for async operations
        then: vi.fn((resolve: (value: any) => void) => resolve({ data: [], error: null }))
      }

      // Override specific table behaviors
      if (table === 'alarm_notifications') {
        mockChain.then = vi.fn((resolve: (value: any) => void) => resolve({ data: [], error: null }))
      }

      return mockChain
    })
  },
  hasSupabaseConfig: true
}))

// Mock Sentry
vi.mock('../lib/sentry', () => ({
  initSentry: vi.fn(),
  setSentryUser: vi.fn(),
  setSentryAlarmContext: vi.fn(),
  setSentryBuildingContext: vi.fn(),
  captureError: vi.fn(),
  captureMessage: vi.fn(),
  Sentry: {
    setUser: vi.fn(),
    setContext: vi.fn(),
    withScope: vi.fn((callback) => callback({ setExtra: vi.fn(), setLevel: vi.fn() })),
    captureException: vi.fn(),
    captureMessage: vi.fn()
  }
}))

// Mock Google Maps
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          PlacesService: any
          AutocompleteService: any
        }
        Map: any
        Marker: any
        InfoWindow: any
      }
    }
  }
}

global.google = {
  maps: {
    places: {
      PlacesService: vi.fn(),
      AutocompleteService: vi.fn()
    },
    Map: vi.fn(),
    Marker: vi.fn(),
    InfoWindow: vi.fn()
  }
}

// Mock Retell AI
vi.mock('../lib/retellAI', () => ({
  default: {
    createPhoneCall: vi.fn(),
    getCall: vi.fn(),
    formatPhoneNumber: vi.fn((phone: string) => phone),
    isValidPhoneNumber: vi.fn(() => true),
    analyzeAcknowledgment: vi.fn(() => ({ acknowledged: false, foundKeywords: [] })),
    mapCallStatus: vi.fn((status: string) => status)
  }
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}
