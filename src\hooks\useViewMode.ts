import { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useUserProfile } from './useUserProfile'

interface UseViewModeReturn {
  viewMode: string
  setViewMode: (mode: string) => Promise<void>
  isLoading: boolean
}

/**
 * Custom hook for managing persistent view mode preferences
 * Provides both immediate persistence (localStorage) and cross-device persistence (user profile)
 */
export const useViewMode = (pageKey: string, defaultMode: string = 'card'): UseViewModeReturn => {
  const { user } = useAuth()
  const { preferences, updatePreferences, loading: profileLoading } = useUserProfile()
  const [viewMode, setViewModeState] = useState(defaultMode)
  const [isLoading, setIsLoading] = useState(true)

  // Local storage key for immediate persistence
  const localStorageKey = `viewMode_${pageKey}`

  // Initialize view mode from localStorage and user preferences
  useEffect(() => {
    const initializeViewMode = () => {
      try {
        // First, try to get from localStorage for immediate availability
        const localViewMode = localStorage.getItem(localStorageKey)

        // Then check user preferences for cross-device sync
        const userViewModePrefs = preferences?.view_mode_preferences || {}
        const userViewMode = userViewModePrefs[pageKey]

        // Define valid modes based on page
        const validModes = pageKey === 'energy' ? ['summary', 'detailed'] : ['card', 'table']

        // Priority: user preferences > localStorage > default
        let initialMode = defaultMode

        if (localViewMode && validModes.includes(localViewMode)) {
          initialMode = localViewMode
        }

        if (userViewMode && validModes.includes(userViewMode)) {
          initialMode = userViewMode
          // Sync localStorage with user preference
          localStorage.setItem(localStorageKey, userViewMode)
        }

        setViewModeState(initialMode)
      } catch (error) {
        console.warn('Error initializing view mode:', error)
        setViewModeState(defaultMode)
      } finally {
        setIsLoading(false)
      }
    }

    // Wait for user profile to load if user is authenticated
    if (user && profileLoading) {
      return
    }

    initializeViewMode()
  }, [pageKey, defaultMode, localStorageKey, preferences, user, profileLoading])

  // Update view mode with persistence
  const setViewMode = useCallback(async (newMode) => {
    // Allow different view modes for different pages
    const validModes = pageKey === 'energy' ? ['summary', 'detailed'] : ['card', 'table']
    if (!validModes.includes(newMode)) {
      console.warn('Invalid view mode:', newMode, 'for page:', pageKey)
      return
    }

    try {
      // Update local state immediately
      setViewModeState(newMode)

      // Save to localStorage for immediate persistence
      localStorage.setItem(localStorageKey, newMode)

      // Save to user preferences for cross-device persistence (if authenticated)
      if (user && preferences && updatePreferences) {
        const currentViewModePrefs = preferences.view_mode_preferences || {}
        const updatedViewModePrefs = {
          ...currentViewModePrefs,
          [pageKey]: newMode
        }

        // Update user preferences in background (don't await to keep UI responsive)
        updatePreferences({
          view_mode_preferences: updatedViewModePrefs
        }).catch(error => {
          console.warn('Failed to save view mode preference to user profile:', error)
          // Don't revert local state - localStorage persistence is still working
        })
      }
    } catch (error) {
      console.error('Error updating view mode:', error)
      // Revert to previous state if there's an error
      const previousMode = localStorage.getItem(localStorageKey) || defaultMode
      setViewModeState(previousMode)
    }
  }, [pageKey, localStorageKey, user, preferences, updatePreferences, defaultMode])

  return {
    viewMode,
    setViewMode,
    isLoading
  }
}

/**
 * Hook for getting view mode preferences for multiple pages
 * Useful for admin interfaces or settings pages
 * 
 * @returns {object} { viewModePreferences, updateViewModePreference, isLoading }
 */
export const useAllViewModes = () => {
  const { user } = useAuth()
  const { preferences, updatePreferences, loading } = useUserProfile()

  const viewModePreferences = useMemo(() =>
    preferences?.view_mode_preferences || {},
    [preferences?.view_mode_preferences]
  )

  const updateViewModePreference = useCallback(async (pageKey, mode) => {
    // Define valid modes based on page
    const validModes = pageKey === 'energy' ? ['summary', 'detailed'] : ['card', 'table']
    if (!validModes.includes(mode)) {
      console.warn('Invalid view mode:', mode, 'for page:', pageKey)
      return { success: false, error: 'Invalid view mode' }
    }

    try {
      const updatedPrefs = {
        ...viewModePreferences,
        [pageKey]: mode
      }

      // Update localStorage for immediate effect
      localStorage.setItem(`viewMode_${pageKey}`, mode)

      // Update user preferences
      if (user && updatePreferences) {
        const result = await updatePreferences({
          view_mode_preferences: updatedPrefs
        })
        
        return { success: !result.error, error: result.error }
      }

      return { success: true, error: null }
    } catch (error) {
      console.error('Error updating view mode preference:', error)
      return { success: false, error: error.message }
    }
  }, [viewModePreferences, user, updatePreferences])

  return {
    viewModePreferences,
    updateViewModePreference,
    isLoading: loading
  }
}

export default useViewMode
