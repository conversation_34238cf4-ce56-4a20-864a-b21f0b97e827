/**
 * Core type definitions for JSC Alarm Call-Out App
 */

import type { User as SupabaseUser } from '@supabase/supabase-js'

// Database types
export interface Database {
  public: {
    Tables: {
      alarm_notifications: {
        Row: AlarmNotification
        Insert: Omit<AlarmNotification, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<AlarmNotification, 'id' | 'created_at'>>
      }
      buildings: {
        Row: Building
        Insert: Omit<Building, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Building, 'id' | 'created_at'>>
      }
      call_outs: {
        Row: CallOut
        Insert: Omit<CallOut, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<CallOut, 'id' | 'created_at'>>
      }
      escalation_contacts: {
        Row: EscalationContact
        Insert: Omit<EscalationContact, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<EscalationContact, 'id' | 'created_at'>>
      }
      user_profiles: {
        Row: UserProfile
        Insert: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<UserProfile, 'id' | 'created_at'>>
      }
    }
  }
}

// Core entity types
export interface AlarmNotification {
  id: string
  building_id: string
  building_alarm_id?: string
  alarm_type_id?: number
  severity_id?: number
  subject?: string
  sender_email?: string
  recipient_email?: string
  message_id?: string
  alarm_time?: string
  alarm_details?: string
  location_details?: string
  body_html?: string
  body_plain?: string
  status: AlarmStatus
  acknowledged_at?: string
  acknowledged_by?: string
  resolved_at?: string
  resolved_by?: string
  webhook_signature?: string
  webhook_timestamp?: number
  webhook_token?: string
  raw_webhook_data?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface Building {
  id: string
  name: string
  address: string
  city: string
  state: string
  zip_code: string
  country: string
  email_address: string
  phone_number?: string
  emergency_contact?: string
  building_type?: string
  square_footage?: number
  floors?: number
  year_built?: number
  location?: {
    lat: number
    lng: number
  }
  timezone?: string
  operating_hours?: Record<string, unknown>
  emergency_procedures?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface CallOut {
  id: string
  alarm_id: string
  contact_id: string
  escalation_level: number
  status: CallOutStatus
  call_status?: CallStatus
  retell_call_id?: string
  call_start_time?: string
  call_end_time?: string
  call_duration?: number
  acknowledged_by_contact?: boolean
  contact_response?: string
  retry_count: number
  max_retries: number
  next_escalation_time?: string
  created_at: string
  updated_at: string
}

export interface EscalationContact {
  id: string
  building_id: string
  contact_name: string
  contact_phone: string
  contact_email?: string
  contact_role: string
  priority_level: number
  is_active: boolean
  availability_schedule?: Record<string, unknown>
  notification_preferences?: Record<string, unknown>
  created_at: string
  updated_at: string
}

export interface UserProfile {
  id: string
  user_id: string
  full_name?: string
  phone_number?: string
  role?: string
  department?: string
  preferences?: Record<string, unknown>
  notification_preferences?: Record<string, unknown>
  created_at: string
  updated_at: string
}

// Enum types
export type AlarmStatus = 'received' | 'acknowledged' | 'resolved' | 'escalated'
export type CallOutStatus = 'pending' | 'calling' | 'acknowledged' | 'failed' | 'escalated' | 'completed'
export type CallStatus = 'calling' | 'answered' | 'no_answer' | 'busy' | 'failed' | 'completed'

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// App component types
export type ViewType = 'bms' | 'alarms' | 'buildings' | 'equipment' | 'energy' | 'workorders' | 'network'

export interface LoginFormProps {
  onDemoMode: () => void
}

// Hook return types
export interface AlarmStats {
  total: number
  byStatus: Record<string, number>
  bySeverity: Record<string, number>
  critical: number
  recent: number
}

export interface UseAlarmsReturn {
  alarms: AlarmNotification[]
  buildings: Building[]
  alarmTypes: AlarmType[]
  severityLevels: SeverityLevel[]
  loading: boolean
  error: string | null
  alarmStats: AlarmStats
  processAlarmWebhook: (webhookData: unknown) => Promise<{ success: boolean; data?: unknown; error?: string }>
  acknowledgeAlarm: (alarmId: string) => Promise<{ success: boolean; error?: string }>
  resolveAlarm: (alarmId: string) => Promise<{ success: boolean; error?: string }>
  refreshAlarms: () => Promise<void>
  triggerEscalation: (alarmId: string) => Promise<{ success: boolean; error?: string }>
  getAlarmsByStatus: (status: AlarmStatus) => AlarmNotification[]
  getAlarmsBySeverity: (severityName: string) => AlarmNotification[]
}

export interface UseAuthReturn {
  user: SupabaseUser | null
  loading: boolean
  signUp: (email: string, password: string) => Promise<{ data: unknown; error: unknown }>
  signIn: (email: string, password: string) => Promise<{ data: unknown; error: unknown }>
  signOut: () => Promise<{ error: unknown }>
}

// API types
export interface RetellAICallRequest {
  toNumber: string
  alarmData: AlarmNotification
  contactData: EscalationContact
  metadata: {
    call_out_id: string
    escalation_level: number
    retry_attempt: number
  }
}

export interface RetellAICallResponse {
  success: boolean
  callId?: string
  error?: string
}

// Utility types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface LoginFormData {
  email: string
  password: string
}

export interface BuildingFormData {
  name: string
  address: string
  city: string
  state: string
  zip_code: string
  country: string
  email_address: string
  phone_number?: string
  emergency_contact?: string
  building_type?: string
  square_footage?: number
  floors?: number
  year_built?: number
  location?: {
    lat: number
    lng: number
  }
  timezone?: string
}

// Hook return types
export interface UseBuildingsReturn {
  buildings: Building[]
  loading: boolean
  error: string | null
  createBuilding: (buildingData: Omit<Building, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; data?: Building; error?: string }>
  updateBuilding: (id: string, updates: Partial<Building>) => Promise<{ success: boolean; data?: Building; error?: string }>
  deleteBuilding: (id: string) => Promise<{ success: boolean; error?: string }>
  toggleBuildingStatus: (id: string) => Promise<{ success: boolean; error?: string }>
  generateUniqueEmail: (buildingName: string) => Promise<string>
  isEmailUnique: (email: string, excludeId?: string) => Promise<boolean>
  getBuildingsByStatus: (status: string) => Building[]
  searchBuildings: (searchTerm: string) => Building[]
}

// Network Device types
export interface NetworkDevice {
  id: string
  station_name: string
  device_type: string
  ip_address: string
  subnet_mask?: string
  host_id?: string
  gateway?: string
  dns_primary?: string
  dns_secondary?: string
  username?: string
  password?: string
  security_passphrase?: string
  software_version?: string
  notes?: string
  building_id: string
  building?: Building
  status: 'active' | 'inactive' | 'maintenance'
  created_at: string
  updated_at: string
}

export interface UseNetworkDevicesReturn {
  devices: NetworkDevice[]
  loading: boolean
  error: string | null
  createDevice: (deviceData: Omit<NetworkDevice, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; data?: NetworkDevice; error?: string }>
  updateDevice: (id: string, updates: Partial<NetworkDevice>) => Promise<{ success: boolean; data?: NetworkDevice; error?: string }>
  deleteDevice: (id: string) => Promise<{ success: boolean; error?: string }>
  toggleDeviceStatus: (id: string) => Promise<{ success: boolean; error?: string }>
  isHostIdUnique: (hostId: string, excludeId?: string) => Promise<boolean>
  isIpAddressUnique: (ipAddress: string, excludeId?: string) => Promise<boolean>
  validateDeviceBatch: (devices: Partial<NetworkDevice>[]) => Promise<{ valid: boolean; errors: string[] }>
  getDevicesByBuilding: (buildingId: string) => NetworkDevice[]
  getDevicesByType: (deviceType: string) => NetworkDevice[]
  getDevicesByStatus: (status: string) => NetworkDevice[]
  searchDevices: (searchTerm: string) => NetworkDevice[]
}

// Additional reference types
export interface AlarmType {
  id: number
  name: string
  description?: string
  color?: string
  icon?: string
  created_at: string
}

export interface SeverityLevel {
  id: number
  name: string
  level: number
  color: string
  description?: string
  created_at: string
}

export interface User {
  id: string
  email: string
  user_metadata?: Record<string, unknown>
  app_metadata?: Record<string, unknown>
  created_at: string
  updated_at: string
}
