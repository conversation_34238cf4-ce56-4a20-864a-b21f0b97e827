import { useState, useEffect, useCallback, useRef } from 'react'

interface UsePlacesAutocompleteOptions {
  requestOptions?: Record<string, unknown>
  debounce?: number
  cache?: number
  cacheKey?: string
  defaultValue?: string
  initOnMount?: boolean
}

interface UsePlacesAutocompleteReturn {
  ready: boolean
  value: string
  suggestions: {
    loading: boolean
    status: string
    data: google.maps.places.AutocompletePrediction[]
  }
  setValue: (value: string, shouldFetchData?: boolean) => void
  clearSuggestions: () => void
  init: () => void
}

/**
 * Custom hook for Google Places Autocomplete using the new AutocompleteSuggestion API
 * This replaces the deprecated use-places-autocomplete library
 */
export const usePlacesAutocompleteNew = ({
  requestOptions = {},
  debounce = 300,
  cache = 24 * 60 * 60, // 24 hours in seconds
  cacheKey = 'upa-new',
  defaultValue = '',
  initOnMount = true,
}: UsePlacesAutocompleteOptions = {}): UsePlacesAutocompleteReturn => {
  const [ready, setReady] = useState(false)
  const [value, setValue] = useState(defaultValue)
  const [suggestions, setSuggestions] = useState({
    loading: false,
    status: '',
    data: []
  })

  const debounceRef = useRef(null)
  const sessionTokenRef = useRef(null)
  const cacheRef = useRef(new Map())

  // Initialize session token
  useEffect(() => {
    if (window.google?.maps?.places?.AutocompleteSessionToken) {
      sessionTokenRef.current = new window.google.maps.places.AutocompleteSessionToken()
    }
  }, [ready])

  // Check if Google Maps API is ready
  const checkReady = useCallback(() => {
    const isReady = !!(
      window.google?.maps?.places?.AutocompleteSuggestion?.fetchAutocompleteSuggestions
    )
    setReady(isReady)
    return isReady
  }, [])

  // Initialize the hook
  const init = useCallback(() => {
    if (checkReady()) {
      console.log('✅ Google Places API (new) initialized successfully')
    } else {
      console.warn('⚠️ Google Places API not ready. Make sure to load the Places library.')
    }
  }, [checkReady])

  // Initialize on mount
  useEffect(() => {
    if (initOnMount) {
      // Check immediately
      if (!checkReady()) {
        // If not ready, check periodically
        const interval = setInterval(() => {
          if (checkReady()) {
            clearInterval(interval)
          }
        }, 100)

        // Clean up after 10 seconds
        setTimeout(() => clearInterval(interval), 10000)
      }
    }
  }, [initOnMount, checkReady])

  // Cache utilities
  const getCacheKey = useCallback((input) => {
    const optionsKey = JSON.stringify(requestOptions)
    return `${cacheKey}-${input}-${optionsKey}`
  }, [cacheKey, requestOptions])

  const getFromCache = useCallback((input) => {
    if (!cache) return null
    
    const key = getCacheKey(input)
    const cached = cacheRef.current.get(key)
    
    if (cached && Date.now() - cached.timestamp < cache * 1000) {
      return cached.data
    }
    
    // Remove expired cache
    cacheRef.current.delete(key)
    return null
  }, [cache, getCacheKey])

  const setToCache = useCallback((input, data) => {
    if (!cache) return
    
    const key = getCacheKey(input)
    cacheRef.current.set(key, {
      data,
      timestamp: Date.now()
    })
  }, [cache, getCacheKey])

  const clearCache = useCallback((key = cacheKey) => {
    if (key === cacheKey) {
      cacheRef.current.clear()
    } else {
      // Clear specific key pattern
      for (const [cacheKey] of cacheRef.current) {
        if (cacheKey.startsWith(key)) {
          cacheRef.current.delete(cacheKey)
        }
      }
    }
  }, [cacheKey])

  // Fetch suggestions from Google Places API
  const fetchSuggestions = useCallback(async (input) => {
    if (!ready || !input.trim()) {
      setSuggestions({ loading: false, status: '', data: [] })
      return
    }

    // Check cache first
    const cached = getFromCache(input)
    if (cached) {
      setSuggestions(cached)
      return
    }

    setSuggestions(prev => ({ ...prev, loading: true }))

    try {
      // Create session token if not exists
      if (!sessionTokenRef.current) {
        sessionTokenRef.current = new window.google.maps.places.AutocompleteSessionToken()
      }

      const request = {
        input,
        sessionToken: sessionTokenRef.current,
        ...requestOptions
      }

      const { suggestions: apiSuggestions } = 
        await window.google.maps.places.AutocompleteSuggestion.fetchAutocompleteSuggestions(request)

      const formattedData = apiSuggestions.map(suggestion => {
        const placePrediction = suggestion.placePrediction
        return {
          place_id: placePrediction.placeId,
          description: placePrediction.text.text,
          structured_formatting: {
            main_text: placePrediction.structuredFormat?.mainText?.text || placePrediction.text.text,
            secondary_text: placePrediction.structuredFormat?.secondaryText?.text || ''
          },
          // Store the original suggestion for advanced usage
          _original: suggestion
        }
      })

      const result = {
        loading: false,
        status: 'OK',
        data: formattedData
      }

      setSuggestions(result)
      setToCache(input, result)

    } catch (error) {
      console.error('Error fetching autocomplete suggestions:', error)

      // Determine appropriate status based on error message
      let status = 'ERROR'
      const errorMessage = error.message || error.toString()

      if (errorMessage.includes('API_NOT_ACTIVATED') || errorMessage.includes('not activated')) {
        status = 'API_NOT_ACTIVATED'
      } else if (errorMessage.includes('INVALID_KEY') || errorMessage.includes('invalid')) {
        status = 'INVALID_KEY'
      } else if (errorMessage.includes('QUOTA_EXCEEDED') || errorMessage.includes('quota')) {
        status = 'QUOTA_EXCEEDED'
      } else if (errorMessage.includes('REQUEST_DENIED') || errorMessage.includes('denied')) {
        status = 'REQUEST_DENIED'
      }

      const errorResult = {
        loading: false,
        status,
        data: [],
        error: {
          message: errorMessage,
          code: status
        }
      }

      setSuggestions(errorResult)
    }
  }, [ready, requestOptions, getFromCache, setToCache])

  // Debounced setValue function
  const setValueWithDebounce = useCallback((newValue, shouldFetchData = true) => {
    setValue(newValue)

    if (!shouldFetchData) {
      return
    }

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    // Set new debounce
    debounceRef.current = setTimeout(() => {
      fetchSuggestions(newValue)
    }, debounce)
  }, [fetchSuggestions, debounce])

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions({
      loading: false,
      status: '',
      data: []
    })
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  return {
    ready,
    value,
    suggestions,
    setValue: setValueWithDebounce,
    clearSuggestions,
    clearCache,
    init
  }
}

/**
 * Utility function to get geocode from address using the new Places API
 */
export const getGeocode = async ({ address, placeId }) => {
  if (!window.google?.maps?.Geocoder) {
    throw new Error('Google Maps Geocoder not available')
  }

  const geocoder = new window.google.maps.Geocoder()
  
  const request = {}
  if (address) {
    request.address = address
  } else if (placeId) {
    request.placeId = placeId
  } else {
    throw new Error('Either address or placeId must be provided')
  }

  return new Promise((resolve, reject) => {
    geocoder.geocode(request, (results, status) => {
      if (status === 'OK') {
        resolve(results)
      } else {
        reject(new Error(`Geocoding failed: ${status}`))
      }
    })
  })
}

/**
 * Utility function to extract lat/lng from geocode result
 */
export const getLatLng = (result) => {
  if (!result?.geometry?.location) {
    throw new Error('Invalid geocode result')
  }

  const location = result.geometry.location
  
  return {
    lat: typeof location.lat === 'function' ? location.lat() : location.lat,
    lng: typeof location.lng === 'function' ? location.lng() : location.lng
  }
}

export default usePlacesAutocompleteNew
