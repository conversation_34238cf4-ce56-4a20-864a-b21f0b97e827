import { useState, useEffect } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'
import type { Building, UseBuildingsReturn } from '@/types'

/**
 * Custom hook for building management operations
 */
export const useBuildings = (): UseBuildingsReturn => {
  const [buildings, setBuildings] = useState<Building[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all buildings
  useEffect(() => {
    const fetchBuildings = async () => {
      if (!hasSupabaseConfig) {
        setError('Supabase not configured. Please add your Supabase URL and API key to .env.local')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('buildings')
          .select('*')
          .order('name')

        if (error) throw error

        setBuildings(data || [])
        
      } catch (err: unknown) {
        console.error('Error fetching buildings:', err)
        setError(err instanceof Error ? err.message : 'Unknown error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchBuildings()
  }, [])

  /**
   * Generate a unique email address for a building
   */
  const generateUniqueEmail = async (): Promise<string> => {
    const generateRandomId = () => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < 10; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }

    let attempts = 0
    const maxAttempts = 10

    while (attempts < maxAttempts) {
      const randomId = generateRandomId()
      const email = `bldg-${randomId}@mg.stieralarms.online`
      
      // Check if email already exists
      const { data, error } = await supabase
        .from('buildings')
        .select('id')
        .eq('email_address', email)

      if (error) {
        console.error('Error checking email existence:', error)
        attempts++
        continue
      }

      // If no data or empty array, email is unique
      if (!data || data.length === 0) {
        return email
      }

      attempts++
    }

    throw new Error('Failed to generate unique email after multiple attempts')
  }

  /**
   * Create a new building
   */
  const createBuilding = async (buildingData: Omit<Building, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; data?: Building; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Generate unique email if not provided
      if (!buildingData.email_address) {
        buildingData.email_address = await generateUniqueEmail()
      }

      const { data, error } = await supabase
        .from('buildings')
        .insert({
          name: buildingData.name,
          address: buildingData.address,
          email_address: buildingData.email_address,
          building_code: buildingData.building_code,
          contact_phone: buildingData.contact_phone,
          contact_email: buildingData.contact_email,
          is_active: buildingData.is_active !== undefined ? buildingData.is_active : true
        })
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev => [...prev, data].sort((a, b) => a.name.localeCompare(b.name)))

      return { success: true, data }
      
    } catch (err: unknown) {
      console.error('Error creating building:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Update an existing building
   */
  const updateBuilding = async (id: string, updates: Partial<Building>): Promise<{ success: boolean; data?: Building; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('buildings')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev =>
        prev.map(building =>
          building.id === id ? data : building
        ).sort((a, b) => a.name.localeCompare(b.name))
      )

      return { success: true, data }
      
    } catch (err: unknown) {
      console.error('Error updating building:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Delete a building
   */
  const deleteBuilding = async (id: string): Promise<{ success: boolean; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { error } = await supabase
        .from('buildings')
        .delete()
        .eq('id', id)

      if (error) throw error

      // Update local state
      setBuildings(prev => prev.filter(building => building.id !== id))

      return { success: true }
      
    } catch (err: unknown) {
      console.error('Error deleting building:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Toggle building active status
   */
  const toggleBuildingStatus = async (id: string): Promise<{ success: boolean; error?: string }> => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Get current building to toggle status
      const currentBuilding = buildings.find(b => b.id === id)
      if (!currentBuilding) {
        return { success: false, error: 'Building not found' }
      }

      const { data, error } = await supabase
        .from('buildings')
        .update({ is_active: !currentBuilding.is_active })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev =>
        prev.map(building =>
          building.id === id ? data : building
        )
      )

      return { success: true, data }
      
    } catch (err: unknown) {
      console.error('Error toggling building status:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Unknown error occurred' }
    }
  }

  /**
   * Check if email address is unique
   */
  const isEmailUnique = async (email: string, excludeId?: string): Promise<boolean> => {
    if (!hasSupabaseConfig) {
      console.warn('Supabase not configured, cannot check email uniqueness')
      return false
    }

    try {
      let query = supabase
        .from('buildings')
        .select('id')
        .eq('email_address', email)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      // Use regular query instead of .single() to avoid 406 errors
      const { data, error } = await query

      if (error) {
        console.error('Error checking email uniqueness:', error)
        return false
      }

      // Email is unique if no records are found
      return !data || data.length === 0
    } catch (err: unknown) {
      console.error('Error checking email uniqueness:', err)
      return false
    }
  }

  /**
   * Get buildings filtered by status
   */
  const getBuildingsByStatus = (status: string): Building[] => {
    return buildings.filter(building => building.status === status)
  }

  /**
   * Search buildings by name or code
   */
  const searchBuildings = (searchTerm: string): Building[] => {
    if (!searchTerm) return buildings

    const term = searchTerm.toLowerCase()
    return buildings.filter(building =>
      (building.name?.toLowerCase() || '').includes(term) ||
      (building.building_code?.toLowerCase() || '').includes(term) ||
      (building.address?.toLowerCase() || '').includes(term)
    )
  }

  return {
    // Data
    buildings,
    loading,
    error,
    
    // Actions
    createBuilding,
    updateBuilding,
    deleteBuilding,
    toggleBuildingStatus,
    generateUniqueEmail,
    isEmailUnique,
    
    // Utilities
    getBuildingsByStatus,
    searchBuildings
  }
}
