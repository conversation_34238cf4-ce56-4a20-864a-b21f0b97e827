import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'
import { initializeErrorFiltering, detectProblematicExtensions } from './lib/errorFilter'
import { initSentry } from './lib/sentry'
import { SentryErrorBoundaryWrapper } from './components/SentryErrorBoundary'

// Initialize Sentry first for comprehensive error tracking
try {
  initSentry()
  console.log('✅ Sentry initialized successfully')
} catch (error: unknown) {
  console.error('❌ Sentry initialization failed:', error)
  console.log('🔧 Continuing without Sentry...')
}

// Initialize error filtering to suppress browser extension errors
// This works in conjunction with Sentry's error filtering
try {
  initializeErrorFiltering()
  console.log('✅ Error filtering initialized successfully')
} catch (error: unknown) {
  console.error('❌ Error filtering initialization failed:', error)
  console.log('🔧 Continuing without error filtering...')
}

// Log extension detection info in development
if (import.meta.env.DEV) {
  try {
    const extensionInfo = detectProblematicExtensions()
    if (extensionInfo.hasExtensions) {
      console.log('🔧 Browser extensions detected:', extensionInfo.extensions.join(', '))
      console.log('🛡️ Error filtering active to reduce console noise')
      console.log('💡 Use enableExtensionErrorDebugging() to see filtered errors')
    }
  } catch (error: unknown) {
    console.error('Extension detection failed:', error)
  }
}

const rootElement = document.getElementById('root')
if (!rootElement) {
  throw new Error('Root element not found')
}

createRoot(rootElement).render(
  <StrictMode>
    <SentryErrorBoundaryWrapper>
      <App />
    </SentryErrorBoundaryWrapper>
  </StrictMode>,
)
